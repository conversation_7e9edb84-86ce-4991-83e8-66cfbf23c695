// 文本补全的使用场景示例

import 'lib/ai_dart/core/chat_provider.dart';
import 'lib/ai_dart/providers/anthropic_provider.dart';

void main() async {
  final provider = AnthropicProvider(
    AnthropicConfig(apiKey: 'your-api-key'),
  );

  // 场景1: 代码补全
  await codeCompletion(provider);
  
  // 场景2: 文章续写
  await articleContinuation(provider);
  
  // 场景3: 创意写作
  await creativeWriting(provider);
  
  // 场景4: 格式化输出
  await structuredOutput(provider);
}

/// 场景1: 代码补全
Future<void> codeCompletion(CompletionProvider provider) async {
  print('=== 代码补全示例 ===');
  
  final request = CompletionRequest(
    prompt: '''
function calculateTotal(items) {
  let total = 0;
  for (let item of items) {
    // 继续写这个函数
''',
    maxTokens: 100,
    temperature: 0.1, // 低温度，更确定性的输出
  );

  try {
    final response = await provider.complete(request);
    print('输入提示: ${request.prompt}');
    print('AI补全: ${response.text}');
  } catch (e) {
    print('错误: $e');
  }
}

/// 场景2: 文章续写
Future<void> articleContinuation(CompletionProvider provider) async {
  print('\n=== 文章续写示例 ===');
  
  final request = CompletionRequest(
    prompt: '''
人工智能的发展正在改变我们的生活方式。从智能手机到自动驾驶汽车，AI技术已经渗透到各个领域。然而，这种快速发展也带来了一些挑战。

首先，''',
    maxTokens: 200,
    temperature: 0.7, // 中等温度，平衡创造性和连贯性
  );

  try {
    final response = await provider.complete(request);
    print('原文开头: ${request.prompt}');
    print('AI续写: ${response.text}');
  } catch (e) {
    print('错误: $e');
  }
}

/// 场景3: 创意写作
Future<void> creativeWriting(CompletionProvider provider) async {
  print('\n=== 创意写作示例 ===');
  
  final request = CompletionRequest(
    prompt: '''
在一个遥远的星球上，有一座漂浮在云端的城市。这座城市的居民都拥有一种特殊的能力——''',
    maxTokens: 150,
    temperature: 0.9, // 高温度，更有创造性
  );

  try {
    final response = await provider.complete(request);
    print('故事开头: ${request.prompt}');
    print('AI续写: ${response.text}');
  } catch (e) {
    print('错误: $e');
  }
}

/// 场景4: 结构化输出
Future<void> structuredOutput(CompletionProvider provider) async {
  print('\n=== 结构化输出示例 ===');
  
  final request = CompletionRequest(
    prompt: '''
产品名称: iPhone 15 Pro
价格: $999
特性列表:
1. ''',
    maxTokens: 100,
    temperature: 0.3,
  );

  try {
    final response = await provider.complete(request);
    print('模板开始: ${request.prompt}');
    print('AI补全: ${response.text}');
  } catch (e) {
    print('错误: $e');
  }
}
